/* USER CODE BEGIN Header */
/**
  * @file           : main.c
  * @brief          : Main program body
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "rtc.h"
#include "spi.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

//更新日期：2025-6-17
//更新功能：
//1.数据合成使用HYS新协议；
//2.温度值采集使用单片机内部温度；
//3.电池电压与温度校准宏定义在GPS.H文件里；


#include <stdio.h>
#include <string.h>
#include "globals.h"
#include "FLASH/bsp_flash.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "GM20.h"
#include "GPS.h"
#include "rtc_sync.h"

#define UART1_RX_BUFFER_SIZE 1
uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE];
#define GPS_BUFFER_SIZE 256
char gps_buffer[GPS_BUFFER_SIZE];
uint16_t gps_buffer_index = 0;

#define SLEEP_DURATION_NORMAL 60
#define SLEEP_DURATION_FIRST_BOOT 90
#define GPS_TIMEOUT_NORMAL 60
#define GPS_TIMEOUT_FIRST_BOOT 120
#define BATTERY_SAMPLE_COUNT 3

uint8_t gm20_rx_byte;
#define GM20_BUFFER_SIZE 256
char gm20_buffer[GM20_BUFFER_SIZE];
uint16_t gm20_buffer_index = 0;

#define IS_GPS_PWR_ON() (HAL_GPIO_ReadPin(GPS_PWR_GPIO_Port, GPS_PWR_Pin) == GPIO_PIN_SET)

uint8_t int_wake_src = 0;

LSM6DS3_Data imuData;
LSM6DS3_Attitude attitude;

void PrintCurrentTime(void);
uint8_t Check_Button_Status(void);
void Enter_Sleep_Mode(uint32_t sleep_seconds);
void UART2_SendString(const char *str);
void UART2_SendStatus(uint8_t status_code, const char *short_msg);
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
HAL_StatusTypeDef Send_Data_To_GM20(const char *data_string);
void GPS_ParseData(void);

HAL_StatusTypeDef Flash_WriteUint32(uint8_t index, uint32_t value);
HAL_StatusTypeDef Flash_ReadUint32(uint8_t index, uint32_t *value);
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void MX_FREERTOS_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// Print current RTC time
void PrintCurrentTime(void) {
  RTC_TimeTypeDef time;
  RTC_DateTypeDef date;
  RTC_GetDateTime(&time, &date);
}

// External variables
extern GPS_Data_t gps_data;
extern float pw;

// External functions
extern void GM20_RxCallback(uint8_t data);

// Read specific data frame from GM20 storage
HAL_StatusTypeDef GM20_ReadSpecificData(uint16_t frame_index, uint16_t *frame_no,
                                       uint16_t *data_length, char *data_content,
                                       uint16_t buffer_size, uint32_t *timestamp)
{
    char cmd[32];

    snprintf(cmd, sizeof(cmd), "AT+CMMQ=%d\r\n", frame_index);

    GM20_FlushBuffer();

    GM20_SendCmd(cmd);

    if (GM20_WaitResponse("+FrameNo:", GM20_RESPONSE_TIMEOUT)) {
        char *p = strstr(gm20RxBuffer, "+FrameNo:");
        if (p != NULL) {
            char temp_data[256] = {0};

            int parsed = sscanf(p, "+FrameNo:%hu,%hu,%255[^,],%u",
                               frame_no, data_length, temp_data, timestamp);

            if (parsed >= 3) {
                strncpy(data_content, temp_data, buffer_size - 1);
                data_content[buffer_size - 1] = '\0';

                printf("Frame %d: %s\r\n", frame_index, data_content);

                if (GM20_WaitResponse("OK", GM20_RESPONSE_TIMEOUT)) {
                    return HAL_OK;
                }
            }
        }
    }

    return HAL_ERROR;
}

// Test function to read specific GM20 data frame
void Test_GM20_ReadSpecificData(uint16_t frame_index)
{
    uint16_t frame_no = 0;
    uint16_t data_length = 0;
    char data_content[256] = {0};
    uint32_t timestamp = 0;

    HAL_StatusTypeDef result = GM20_ReadSpecificData(frame_index, &frame_no, &data_length,
                                                    data_content, sizeof(data_content),
                                                    &timestamp);

    if (result == HAL_OK) {
        printf("Frame %d: %s\r\n", frame_index, data_content);
    }
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_LPUART1_UART_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_I2C1_Init();
  MX_RTC_Init();
  MX_SPI1_Init();
  /* USER CODE BEGIN 2 */
  // Initialize UART receive buffers
  memset(uart1_rx_buffer, 0, UART1_RX_BUFFER_SIZE);
  gps_buffer_index = 0;
  memset(gm20_buffer, 0, GM20_BUFFER_SIZE);
  gm20_buffer_index = 0;

  // Start UART receive interrupts
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);  // GPS
  HAL_UART_Receive_IT(&hlpuart1, &gm20_rx_byte, 1);      // GM20

  // Enable backup domain access
  HAL_PWR_EnableBkUpAccess();

  // Load RTC settings and initialize sync mechanism
  RTC_LoadSettings();
  RTC_Sync_Init();
  PrintCurrentTime();

  //设置GM20休眠模式
  #define GM20_SLEEP_TIMEOUT_SECONDS 1  // 0=no sleep, 1=sleep mode
	//初始化GM20串口 启动设定
  printf("Initializing GM20 module...\r\n");
  RF_PWR_ON;
  HAL_Delay(1000);
  __HAL_RCC_LPUART1_CLK_ENABLE();
  MX_LPUART1_UART_Init();
  HAL_UART_Receive_IT(&hlpuart1, &gm20_rx_byte, 1);
  HAL_Delay(100);
	//打印输出GM20休眠模式
  printf("Setting GM20 sleep mode: %s\r\n", GM20_SLEEP_TIMEOUT_SECONDS ? "Enabled" : "Disabled");
  if (GM20_InitWithSleepTime(GM20_SLEEP_TIMEOUT_SECONDS) == GM20_OK) {
//    printf("GM20 module initialized successfully\r\n");
  } else {
    printf("Failed to initialize GM20 module\r\n");
  }

	 //删除GM20内部待发数据
  if (GM20_ClearStorageData() == GM20_OK) {
    printf("GM20 storage data cleared successfully\r\n");
  } else {
    printf("Failed to clear GM20 storage data\r\n");
  }

			//读取GM20内部待发数据
//		for (int i = 1; i <= 28; i++) {
//			Test_GM20_ReadSpecificData(i);
//		}


  /* USER CODE END 2 */

  /* Call init function for freertos objects (in cmsis_os2.c) */
  MX_FREERTOS_Init();

  /* Start scheduler */
  osKernelStart();

  /* We should never get here as control is now taken by the scheduler */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLLMUL_4;
  RCC_OscInitStruct.PLL.PLLDIV = RCC_PLLDIV_2;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_1) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1|RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
// Printf redirection to UART
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

// UART receive complete callback
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if(huart->Instance == USART1)  // GPS module
  {
    if (gps_buffer_index < GPS_BUFFER_SIZE - 1) {
      gps_buffer[gps_buffer_index++] = uart1_rx_buffer[0];
      gps_buffer[gps_buffer_index] = '\0';

      // Check for complete NMEA sentence
      if (uart1_rx_buffer[0] == '\n') {
        gps_new_data = 1;
      }
    } else {
      // Buffer full, reset
      gps_buffer_index = 0;
      memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    }

    HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  }
  else if(huart->Instance == LPUART1)  // GM20 module
  {
    GM20_RxCallback(gm20_rx_byte);

    HAL_UART_Receive_IT(&hlpuart1, &gm20_rx_byte, 1);
  }
}

// Create complete data string for transmission
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size)
{
    char time_str[20] = {0};
    char sn_buffer[32] = {0};
    int8_t signal_quality = -128;

    // Get GM20 module information
    if (GM20_GetSN(sn_buffer) != GM20_OK) {
        strcpy(sn_buffer, "00000000");
    }

    if (GM20_GetSignalQuality(&signal_quality) != GM20_OK) {
        signal_quality = -128;
    }

    // Smart time source selection logic
    uint8_t gps_time_valid = (gps_data.valid && gps_data.hour <= 23 &&
                             gps_data.minute <= 59 && gps_data.second <= 59);
    uint8_t gps_date_valid = (gps_data.year >= 2020 && gps_data.month >= 1 &&
                             gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31);

    // Get RTC time as primary source when GPS invalid
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    RTC_GetDateTime(&rtc_time, &rtc_date);

    // Time source priority: GPS valid and has coordinates -> use GPS time, otherwise use RTC
    if (gps_data.valid && gps_data.latitude > 0 && gps_data.longitude > 0) {
        if (gps_time_valid && gps_date_valid) {
            // Use complete GPS time when GPS has valid positioning
            snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                    gps_data.hour, gps_data.minute, gps_data.second,
                    gps_data.day, gps_data.month, gps_data.year % 100);
        } else if (gps_time_valid && !gps_date_valid) {
            // Use GPS time + RTC date when GPS time valid but date invalid
            snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                    gps_data.hour, gps_data.minute, gps_data.second,
                    rtc_date.Date, rtc_date.Month, rtc_date.Year);
        } else {
            // GPS positioning valid but time invalid, use RTC time
            snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                    rtc_time.Hours, rtc_time.Minutes, rtc_time.Seconds,
                    rtc_date.Date, rtc_date.Month, rtc_date.Year);
        }
    } else {
        // GPS invalid or no positioning, always use RTC time and date
        snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                rtc_time.Hours, rtc_time.Minutes, rtc_time.Seconds,
                rtc_date.Date, rtc_date.Month, rtc_date.Year);
    }

    // GPS data or default values
    float longitude = gps_data.longitude;
    float latitude = gps_data.latitude;
    float altitude = gps_data.altitude;
    uint8_t fix_quality = gps_data.fix_quality;
    uint8_t satellites = gps_data.satellites;
    float speed = gps_data.speed;
    float hdop = gps_data.hdop;
    float pdop = gps_data.pdop;
    float course = gps_data.course;

    if (!gps_data.valid) {
        // Use default values when GPS is invalid
        longitude = 0.0;
        latitude = 0.0;
        altitude = 0.0;
        fix_quality = 0;
        satellites = 0;
        speed = 0.0;
        hdop = 99.9;
        pdop = 99.9;
        course = 0.0;
    } else {
        // GPS valid but check for missing precision data
        if (hdop <= 0.0) {
            hdop = 2.5;  // Use reasonable default when HDOP not available
        }
        if (pdop <= 0.0 || pdop >= 99.0) {
            pdop = 3.0;  // Use reasonable default when PDOP not available or invalid
        }
    }

    // Get MCU internal temperature
    extern float mcu_temp;

    // Create data content string
    char data_content[200];
    snprintf(data_content, sizeof(data_content),
            "S+%.5f+%.5f+%s+%.1f+%d+%d+%.1f+%.2f+%.1f+%.1f+%.1f+%.1f+%.1f+%.1f+%.2f+%d+%s+E",
            longitude,
            latitude,
            time_str,
            altitude,
            fix_quality,
            satellites,
            speed,
            pw,
            mcu_temp,  // 使用MCU内部温度替换三轴传感器温度
            hdop,
            pdop,
            attitude.roll,
            attitude.pitch,
            attitude.yaw,
            course,
            signal_quality,
            sn_buffer
            );

    // Build complete string with length header
    uint16_t data_length = strlen(data_content);
    if (data_length < 100) {
        snprintf(output_buffer, buffer_size, "HY0%d%s", data_length, data_content);
    } else {
        snprintf(output_buffer, buffer_size, "HY%d%s", data_length, data_content);
    }

    return HAL_OK;
}

// Send data to GM20 module
HAL_StatusTypeDef Send_Data_To_GM20(const char *data_string)
{
    uint16_t frameNo = 0;
    uint16_t data_count = 0;

    uint16_t data_length = strlen(data_string);

    printf("Sending to GM20: %s\r\n", data_string);

    // Send complete data packet to GM20 storage
    GM20_StatusTypeDef result = GM20_SendStringData((char*)data_string, data_length, &frameNo);

    if (result == GM20_OK) {
//        printf("Data sent successfully\r\n");

        // Query pending data count
        GM20_StatusTypeDef query_result = GM20_QueryDataCount(&data_count);
        if (query_result == GM20_OK) {
            printf("Pending data count: %d\r\n", data_count);
        }

        return HAL_OK;
    } else {
        printf("Data send failed\r\n");
        return HAL_ERROR;
    }
}

// Parse GPS data from buffer
void GPS_ParseData(void)
{
    char *line_start = gps_buffer;
    char *line_end;

    // Find complete NMEA sentences
    while ((line_end = strchr(line_start, '\n')) != NULL) {
        *line_end = '\0';  // Temporarily terminate string

        // Parse NMEA sentence
        if (strlen(line_start) > 6) {
            GPS_ParseNMEA(line_start, &gps_data);
        }

        line_start = line_end + 1;
    }

    // Move remaining data to buffer start
    if (line_start < gps_buffer + gps_buffer_index) {
        uint16_t remaining = gps_buffer + gps_buffer_index - line_start;
        memmove(gps_buffer, line_start, remaining);
        gps_buffer_index = remaining;
        gps_buffer[gps_buffer_index] = '\0';
    } else {
        // No remaining data, clear buffer
        gps_buffer_index = 0;
        gps_buffer[0] = '\0';
    }
}
/* USER CODE END 4 */

/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */

  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM6) {
    HAL_IncTick();
  }
  /* USER CODE BEGIN Callback 1 */

  /* USER CODE END Callback 1 */
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  // User can add his own implementation to report the HAL error return state
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  // User can add his own implementation to report the file name and line number
  // ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line)
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
